package com.ruoyi.app.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.fuguang.service.ITaskPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.service.IAppTaskService;
import com.ruoyi.fuguang.service.IAppTaskApplicationService;
import com.ruoyi.fuguang.domain.AppTaskApplication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP任务接口控制器
 * 提供任务查询、发布、接取、完成等功能
 *
 * <AUTHOR>
 */
@Api(tags = "APP任务接口", description = "APP任务管理相关接口")
@RestController("appTaskApiController")
@RequestMapping("/app/task")
public class AppTaskController extends BaseController
{
    @Autowired
    private IAppTaskService appTaskService;

    @Autowired
    private IAppTaskApplicationService appTaskApplicationService;

    /**
     * 查询任务列表
     * 支持按条件筛选任务，包括任务类型、状态、地理位置等
     *
     * @param appTask 查询条件
     * @return 任务列表
     */
    @Anonymous
    @ApiOperation(value = "查询任务列表",
                  notes = "获取任务列表，支持分页和条件筛选，包括任务类型、状态、地理位置等")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回任务列表")
    })
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam(value = "查询条件", required = false) AppTask appTask)
    {
        Map<String,Object> map = new HashMap<>();
        map.put("appList",1);
        appTask.setParams(map);
        startPage();
        List<AppTask> list = appTaskService.selectAppTaskList(appTask);
        return getDataTable(list);
    }

    /**
     * 获取任务详细信息
     * 查看任务详情时会自动增加浏览次数
     *
     * @param taskId 任务ID
     * @return 任务详细信息
     */
    @ApiOperation(value = "获取任务详细信息",
                  notes = "根据任务ID获取任务详细信息，会自动增加浏览次数")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回任务详细信息"),
        @ApiResponse(code = 500, message = "任务不存在")
    })
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@ApiParam(value = "任务ID", required = true) @PathVariable("taskId") Long taskId)
    {
        // 增加浏览次数
        appTaskService.increaseViewCount(taskId);
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        return success(task);
    }

    /**
     * 发布新任务
     * 用户发布新的任务，需要填写任务详情和报酬
     *
     * @param appTask 任务信息
     * @return 发布结果
     */
    @ApiOperation(value = "发布新任务",
                  notes = "用户发布新任务，需要填写任务标题、描述、报酬、截止时间等信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "发布成功"),
        @ApiResponse(code = 500, message = "发布失败，参数错误或余额不足")
    })
    @Log(title = "APP任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam(value = "任务信息", required = true) @RequestBody AppTask appTask)
    {
        Long userId = getUserId();
        appTask.setPublisherId(userId);
        try {
            // 发布任务
            return success( appTaskService.insertAppTask(appTask));
        } catch (Exception e) {
            return error("任务发布失败：" + e.getMessage());
        }
    }

    /**
     * 修改任务
     */
    @ApiOperation("修改任务")
    @Log(title = "APP任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppTask appTask)
    {
        Long userId = getUserId();
        // 验证是否为任务发布者
        AppTask existTask = appTaskService.selectAppTaskByTaskId(appTask.getTaskId());
        if (existTask == null || !existTask.getPublisherId().equals(userId)) {
            return error("无权限修改此任务");
        }
        if(!existTask.getTaskStatus().equals("0")){
            return error("任务已经发布，无法修改");
        }
        return toAjax(appTaskService.updateAppTask(appTask));
    }
    /**
     * 完成任务
     * 任务接收者提交任务完成，等待发布者确认
     *
     * @param taskId 任务ID
     * @return 完成结果
     */
    @ApiOperation(value = "完成任务",
                  notes = "任务接收者提交任务完成，任务状态变为待确认，等待发布者确认")
    @ApiResponses({
        @ApiResponse(code = 200, message = "提交成功，等待发布者确认"),
        @ApiResponse(code = 500, message = "提交失败，任务不存在、状态不允许或无权限操作")
    })
    @Log(title = "完成任务", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{taskId}")
    public AjaxResult completeTask(@ApiParam(value = "任务ID", required = true) @PathVariable Long taskId)
    {
        Long userId = getUserId();
        // 验证任务状态和权限
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        if (task == null) {
            return error("任务不存在");
        }
        if (!"2".equals(task.getTaskStatus())) {
            return error("任务状态不允许完成");
        }
        if (!task.getReceiverId().equals(userId)) {
            return error("只有任务接收者可以完成任务");
        }
        return appTaskService.completeTask(taskId);
    }

    /**
     * 取消任务
     */
    @ApiOperation("取消任务")
    @Log(title = "取消任务", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{taskId}")
    public AjaxResult cancelTask(@PathVariable Long taskId)
    {
        Long userId = getUserId();
        
        // 验证任务状态和权限
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        if (task == null) {
            return error("任务不存在");
        }
        if (!"0".equals(task.getTaskStatus()) && !"1".equals(task.getTaskStatus()) &&!"6".equals(task.getTaskStatus())) {
            return error("任务无法取消");
        }
        if (!task.getPublisherId().equals(userId)) {
            return error("只有任务发布者可以取消任务");
        }
        int result = appTaskService.cancelTask(taskId);
        return toAjax(result);
    }

    /**
     * 查询我发布的任务
     */
    @ApiOperation("查询我发布的任务")
    @GetMapping("/my-published")
    public TableDataInfo getMyPublishedTasks()
    {
        Long userId = getUserId();
        startPage();
        List<AppTask> tasks = appTaskService.selectTasksByPublisher(userId);
        return getDataTable(tasks);
    }

    /**
     * 查询我接取的任务
     */
    @ApiOperation("查询我接取的任务")
    @GetMapping("/my-received")
    public TableDataInfo getMyReceivedTasks()
    {
        Long userId = getUserId();
        startPage();
        List<AppTask> tasks = appTaskService.selectTasksByReceiver(userId);
        return getDataTable(tasks);
    }

    /**
     * 查询热门任务
     */
    @Anonymous
    @ApiOperation("查询热门任务")
    @GetMapping("/hot")
    public TableDataInfo getHotTasks(
            @ApiParam("经度") @RequestParam(required = false) String longitude,
            @ApiParam("纬度") @RequestParam(required = false) String latitude)
    {
        startPage();
        List<AppTask> tasks = appTaskService.selectHotTaskList(longitude, latitude);
        return getDataTable(tasks);
    }

    /**
     * 申请任务
     * 用户申请接取发布的任务
     *
     * @param taskId 任务ID
     * @param applicationReason 申请理由
     * @return 申请结果
     */
    @ApiOperation(value = "申请任务",
                  notes = "用户申请接取发布的任务，申请后等待发布者确认")
    @ApiResponses({
        @ApiResponse(code = 200, message = "申请成功"),
        @ApiResponse(code = 500, message = "申请失败，任务不存在、状态不允许或已申请过")
    })
    @Log(title = "申请任务", businessType = BusinessType.INSERT)
    @PostMapping("/apply/{taskId}")
    public AjaxResult applyForTask(@ApiParam(value = "任务ID", required = true) @PathVariable Long taskId,
                                   @ApiParam(value = "申请理由", required = false) @RequestParam(required = false) String applicationReason)
    {
        Long userId = getUserId();
        try {
            int result = appTaskApplicationService.applyForTask(taskId, userId, applicationReason);
            return toAjax(result);
        } catch (Exception e) {
            return error("申请失败：" + e.getMessage());
        }
    }

    /**
     * 查询我的申请列表
     * 获取当前用户的所有任务申请记录，包含发布者的信用分、性别、实名姓名（脱敏）、用户手机号（脱敏）
     *
     * @return 申请列表
     */
    @ApiOperation(value = "查询我的申请列表",
                  notes = "获取当前用户的所有任务申请记录，包括申请状态和任务信息，以及发布者的详细信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回申请列表")
    })
    @GetMapping("/applications/my")
    public TableDataInfo getMyApplications()
    {
        Long userId = getUserId();
        startPage();
        List<AppTaskApplication> list = appTaskApplicationService.selectAppTaskApplicationListByApplicantIdWithPublisherInfo(userId);
        return getDataTable(list);
    }

    /**
     * 查询任务申请列表
     * 发布者查看自己发布任务的申请情况，包含申请用户的信用分、性别、实名姓名（脱敏）、用户手机号（脱敏）
     *
     * @param taskId 任务ID
     * @return 申请列表
     */
    @ApiOperation(value = "查询任务申请列表",
                  notes = "发布者查看自己发布任务的申请情况，用于选择接取人，包含申请用户的详细信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功，返回申请列表"),
        @ApiResponse(code = 500, message = "无权限查看或任务不存在")
    })
    @GetMapping("/applications/{taskId}")
    public TableDataInfo getTaskApplications(@ApiParam(value = "任务ID", required = true) @PathVariable Long taskId)
    {
        Long userId = getUserId();
        // 验证权限（只有发布者可以查看申请列表）
        AppTask task = appTaskService.selectAppTaskByTaskId(taskId);
        if (task == null) {
            throw new ServiceException("任务不存在");
        }
        if (!task.getPublisherId().equals(userId)) {
            throw new ServiceException("无权限查看此任务的申请列表");
        }
        startPage();
        List<AppTaskApplication> list = appTaskApplicationService.selectAppTaskApplicationListByTaskIdWithApplicantInfo(taskId);
        return getDataTable(list);
    }

    /**
     * 确认申请（发布者选择接取人）
     * 发布者从申请列表中选择一个申请者作为任务接收人
     *
     * @param applicationId 申请ID
     * @return 确认结果
     */
    @ApiOperation(value = "确认申请",
                  notes = "发布者从申请列表中选择一个申请者作为任务接收人，任务状态变为进行中")
    @ApiResponses({
        @ApiResponse(code = 200, message = "确认成功"),
        @ApiResponse(code = 500, message = "确认失败，申请不存在、状态不允许或无权限操作")
    })
    @Log(title = "确认申请", businessType = BusinessType.UPDATE)
    @PostMapping("/applications/confirm/{applicationId}")
    public AjaxResult confirmApplication(@ApiParam(value = "申请ID", required = true) @PathVariable Long applicationId)
    {
        Long userId = getUserId();
        try {
            int result = appTaskApplicationService.confirmApplication(applicationId, userId);
            return toAjax(result);
        } catch (Exception e) {
            return error("确认失败：" + e.getMessage());
        }
    }

    /**
     * 拒绝申请
     * 发布者拒绝某个申请
     *
     * @param applicationId 申请ID
     * @return 拒绝结果
     */
    @ApiOperation(value = "拒绝申请",
                  notes = "发布者拒绝某个申请")
    @ApiResponses({
        @ApiResponse(code = 200, message = "拒绝成功"),
        @ApiResponse(code = 500, message = "拒绝失败，申请不存在、状态不允许或无权限操作")
    })
    @Log(title = "拒绝申请", businessType = BusinessType.UPDATE)
    @PostMapping("/applications/reject/{applicationId}")
    public AjaxResult rejectApplication(@ApiParam(value = "申请ID", required = true) @PathVariable Long applicationId)
    {
        Long userId = getUserId();
        try {
            int result = appTaskApplicationService.rejectApplication(applicationId, userId);
            return toAjax(result);
        } catch (Exception e) {
            return error("拒绝失败：" + e.getMessage());
        }
    }

    /**
     * 取消申请
     * 申请人取消自己的申请
     *
     * @param applicationId 申请ID
     * @return 取消结果
     */
    @ApiOperation(value = "取消申请",
                  notes = "申请人取消自己的申请")
    @ApiResponses({
        @ApiResponse(code = 200, message = "取消成功"),
        @ApiResponse(code = 500, message = "取消失败，申请不存在、状态不允许或无权限操作")
    })
    @Log(title = "取消申请", businessType = BusinessType.UPDATE)
    @PostMapping("/applications/cancel/{applicationId}")
    public AjaxResult cancelApplication(@ApiParam(value = "申请ID", required = true) @PathVariable Long applicationId)
    {
        Long userId = getUserId();
        try {
            int result = appTaskApplicationService.cancelApplication(applicationId, userId);
            return toAjax(result);
        } catch (Exception e) {
            return error("取消失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否已申请任务
     * 用户检查自己是否已申请某个任务
     *
     * @param taskId 任务ID
     * @return 检查结果
     */
    @ApiOperation(value = "检查是否已申请任务",
                  notes = "用户检查自己是否已申请某个任务")
    @ApiResponses({
        @ApiResponse(code = 200, message = "检查成功，返回是否已申请")
    })
    @GetMapping("/applications/check/{taskId}")
    public AjaxResult checkApplicationStatus(@ApiParam(value = "任务ID", required = true) @PathVariable Long taskId)
    {
        Long userId = getUserId();
        boolean hasApplied = appTaskApplicationService.hasAppliedForTask(taskId, userId);
        return success(hasApplied);
    }
}
